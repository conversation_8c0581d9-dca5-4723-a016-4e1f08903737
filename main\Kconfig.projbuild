menu "Xiaozhi Assistant"

    config OTA_VERSION_URL
        string "OTA Version URL"
        # default "https://api.tenclass.net/xiaozhi/ota/"
        default "http://47.111.85.129:8084/api/device/ota"
        help
            The application will access this URL to check for updates.
    
    
    choice
        prompt "语言选择"
        default LANGUAGE_ZH_CN
        help
            Select device display language
    
        config LANGUAGE_ZH_CN
            bool "Chinese"
        config LANGUAGE_ZH_TW
            bool "Chinese Traditional"
        config LANGUAGE_EN_US
            bool "English"
        config LANGUAGE_JA_JP
            bool "Japanese"
    endchoice
    
    
    choice CONNECTION_TYPE
        prompt "Connection Type"
        default CONNECTION_TYPE_WEBSOCKET
        help
            网络数据传输协议
        config CONNECTION_TYPE_MQTT_UDP
            bool "MQTT + UDP"
        config CONNECTION_TYPE_WEBSOCKET
            bool "Websocket"
    endchoice
    
    config WEBSOCKET_URL
        depends on CONNECTION_TYPE_WEBSOCKET
        string "Websocket URL"
        # default "wss://api.tenclass.net/xiaozhi/v1/"
        default "ws://47.111.85.129:8091/ws/xiaozhi/v1/"
        help
            Communication with the server through websocket after wake up.
    
    config WEBSOCKET_ACCESS_TOKEN
        depends on CONNECTION_TYPE_WEBSOCKET
        string "Websocket Access Token"
        default "test-token"
        help
            Access token for websocket communication.
    
    choice BOARD_TYPE
        prompt "Board Type"
        default BOARD_TYPE_ZHENGCHEN_1_54TFT_WIFI
        help
            Board type. 开发板类型
        config BOARD_TYPE_BREAD_COMPACT_WIFI
            bool "面包板新版接线（WiFi）"
        config BOARD_TYPE_BREAD_COMPACT_WIFI_LCD
            bool "面包板新版接线（WiFi）+ LCD"
        config BOARD_TYPE_BREAD_COMPACT_ML307
            bool "面包板新版接线（ML307 AT）"
        config BOARD_TYPE_BREAD_COMPACT_ESP32
            bool "面包板（WiFi） ESP32 DevKit"
        config BOARD_TYPE_BREAD_COMPACT_ESP32_LCD
            bool "面包板（WiFi+ LCD） ESP32 DevKit"
        config BOARD_TYPE_ESP32_CGC
            bool "ESP32 CGC"
        config BOARD_TYPE_ESP_BOX_3
            bool "ESP BOX 3"
        config BOARD_TYPE_ESP_BOX
            bool "ESP BOX"
        config BOARD_TYPE_ESP_BOX_LITE
            bool "ESP BOX Lite"        
        config BOARD_TYPE_KEVIN_BOX_1
            bool "Kevin Box 1"
        config BOARD_TYPE_KEVIN_BOX_2
            bool "Kevin Box 2"
        config BOARD_TYPE_KEVIN_C3
            bool "Kevin C3"
        config BOARD_TYPE_KEVIN_SP_V3_DEV
            bool "Kevin SP V3开发板"
        config BOARD_TYPE_KEVIN_SP_V4_DEV
            bool "Kevin SP V4开发板"
        config BOARD_TYPE_KEVIN_YUYING_313LCD
            bool "鱼鹰科技3.13LCD开发板"
        config BOARD_TYPE_LICHUANG_DEV
            bool "立创·实战派ESP32-S3开发板"
        config BOARD_TYPE_LICHUANG_C3_DEV
            bool "立创·实战派ESP32-C3开发板"
        config BOARD_TYPE_DF_K10
            bool "DFRobot 行空板 k10"
        config BOARD_TYPE_MAGICLICK_2P4
            bool "神奇按钮 Magiclick_2.4"
        config BOARD_TYPE_MAGICLICK_2P5
            bool "神奇按钮 Magiclick_2.5"
        config BOARD_TYPE_MAGICLICK_C3
            bool "神奇按钮 Magiclick_C3"
        config BOARD_TYPE_MAGICLICK_C3_V2
            bool "神奇按钮 Magiclick_C3_v2"
        config BOARD_TYPE_M5STACK_CORE_S3
            bool "M5Stack CoreS3"
        config BOARD_TYPE_ATOMS3_ECHO_BASE
            bool "AtomS3 + Echo Base"
        config BOARD_TYPE_ATOMS3R_ECHO_BASE
            bool "AtomS3R + Echo Base"
        config BOARD_TYPE_ATOMS3R_CAM_M12_ECHO_BASE
            bool "AtomS3R CAM/M12 + Echo Base"
        config BOARD_TYPE_ATOMMATRIX_ECHO_BASE
            bool "AtomMatrix + Echo Base"
        config BOARD_TYPE_XMINI_C3
            bool "虾哥 Mini C3"
        config BOARD_TYPE_ESP32S3_KORVO2_V3
            bool "ESP32S3_KORVO2_V3开发板"
        config BOARD_TYPE_ESP_SPARKBOT
            bool "ESP-SparkBot开发板"
        config BOARD_TYPE_ESP32S3_Touch_AMOLED_1_8
            bool "Waveshare ESP32-S3-Touch-AMOLED-1.8"
        config BOARD_TYPE_ESP32S3_Touch_LCD_1_85C
            bool "Waveshare ESP32-S3-Touch-LCD-1.85C"
        config BOARD_TYPE_ESP32S3_Touch_LCD_1_85
            bool "Waveshare ESP32-S3-Touch-LCD-1.85"
        config BOARD_TYPE_ESP32S3_Touch_LCD_1_46
            bool "Waveshare ESP32-S3-Touch-LCD-1.46"
        config BOARD_TYPE_ESP32S3_Touch_LCD_3_5
            bool "Waveshare ESP32-S3-Touch-LCD-3.5"
        config BOARD_TYPE_TUDOUZI
            bool "土豆子"
        config BOARD_TYPE_LILYGO_T_CIRCLE_S3
            bool "LILYGO T-Circle-S3"
        config BOARD_TYPE_LILYGO_T_CAMERAPLUS_S3
            bool "LILYGO T-CameraPlus-S3"
        config BOARD_TYPE_MOVECALL_MOJI_ESP32S3
             bool "Movecall Moji 小智AI衍生版"
        config BOARD_TYPE_MOVECALL_CUICAN_ESP32S3
             bool "Movecall CuiCan 璀璨·AI吊坠"
        config BOARD_TYPE_ATK_DNESP32S3
            bool "正点原子DNESP32S3开发板"
        config BOARD_TYPE_ATK_DNESP32S3_BOX
            bool "正点原子DNESP32S3-BOX"
        config BOARD_TYPE_DU_CHATX
            bool "嘟嘟开发板CHATX(wifi)"
        config BOARD_TYPE_ESP32S3_Taiji_Pi
            bool "太极小派esp32s3"
        config BOARD_TYPE_XINGZHI_Cube_0_85TFT_WIFI
            bool "无名科技星智0.85(WIFI)"
        config BOARD_TYPE_XINGZHI_Cube_0_85TFT_ML307
            bool "无名科技星智0.85(ML307)"
        config BOARD_TYPE_ZHENGCHEN_0_96OLED_WIFI
            bool "苑学0.96(WIFI)"
        config BOARD_TYPE_ZHENGCHEN_0_96OLED_ML307
            bool "苑学0.96(ML307)"
        config BOARD_TYPE_ZHENGCHEN_1_54TFT_WIFI
            bool "苑学1.54(WIFI)"
        config BOARD_TYPE_ZHENGCHEN_1_54TFT_ML307
            bool "苑学1.54(ML307)"
        config BOARD_TYPE_SENSECAP_WATCHER
            bool "SenseCAP Watcher"
    endchoice
    
    choice DISPLAY_OLED_TYPE
        depends on BOARD_TYPE_BREAD_COMPACT_WIFI || BOARD_TYPE_BREAD_COMPACT_ML307 || BOARD_TYPE_BREAD_COMPACT_ESP32
        prompt "OLED Type"
        default OLED_SSD1306_128X32
        help
            OLED 屏幕类型选择
        config OLED_SSD1306_128X32
            bool "SSD1306, 分辨率128*32"
        config OLED_SSD1306_128X64
            bool "SSD1306, 分辨率128*64"
        config OLED_SH1106_128X64
            bool "SH1106, 分辨率128*64"
    endchoice
    
    choice DISPLAY_LCD_TYPE
        depends on BOARD_TYPE_BREAD_COMPACT_WIFI_LCD || BOARD_TYPE_BREAD_COMPACT_ESP32_LCD || BOARD_TYPE_ESP32_CGC
        prompt "LCD Type"
        default LCD_ST7789_240X320
        help
            屏幕类型选择
        config LCD_ST7789_240X320
            bool "ST7789, 分辨率240*320, IPS"
        config LCD_ST7789_240X320_NO_IPS
            bool "ST7789, 分辨率240*320, 非IPS"
        config LCD_ST7789_170X320
            bool "ST7789, 分辨率170*320"
        config LCD_ST7789_172X320
            bool "ST7789, 分辨率172*320"
        config LCD_ST7789_240X280
            bool "ST7789, 分辨率240*280"
        config LCD_ST7789_240X240
            bool "ST7789, 分辨率240*240"
        config LCD_ST7789_240X240_7PIN
            bool "ST7789, 分辨率240*240, 7PIN"
        config LCD_ST7789_240X135
            bool "ST7789, 分辨率240*135"
        config LCD_ST7735_128X160
            bool "ST7735, 分辨率128*160"
        config LCD_ST7735_128X128
            bool "ST7735, 分辨率128*128"
        config LCD_ST7796_320X480
            bool "ST7796, 分辨率320*480 IPS"
        config LCD_ST7796_320X480_NO_IPS
            bool "ST7796, 分辨率320*480, 非IPS"    
        config LCD_ILI9341_240X320
            bool "ILI9341, 分辨率240*320"
        config LCD_ILI9341_240X320_NO_IPS
            bool "ILI9341, 分辨率240*320, 非IPS"
        config LCD_GC9A01_240X240
            bool "GC9A01, 分辨率240*240, 圆屏"
        config LCD_CUSTOM
            bool "自定义屏幕参数"
    endchoice
    
    choice DISPLAY_ESP32S3_KORVO2_V3
        depends on BOARD_TYPE_ESP32S3_KORVO2_V3
        prompt "ESP32S3_KORVO2_V3 LCD Type"
        default LCD_ST7789
        help
            屏幕类型选择
        config LCD_ST7789
            bool "ST7789, 分辨率240*280"
        config LCD_ILI9341
            bool "ILI9341, 分辨率240*320"
    endchoice
    
    config USE_WECHAT_MESSAGE_STYLE
        bool "使用微信聊天界面风格"
        default n
        help
            使用微信聊天界面风格
    
    config USE_WAKE_WORD_DETECT
        bool "启用唤醒词检测"
        default y
        depends on IDF_TARGET_ESP32S3 && SPIRAM
        help
            需要 ESP32 S3 与 AFE 支持
    
    config USE_AUDIO_PROCESSOR
        bool "启用音频降噪、增益处理"
        default y
        depends on IDF_TARGET_ESP32S3 && SPIRAM
        help
            需要 ESP32 S3 与 AFE 支持
    
    config USE_REALTIME_CHAT
        bool "启用可语音打断的实时对话模式（需要 AEC 支持）"
        default n
        depends on USE_AUDIO_PROCESSOR && (BOARD_TYPE_ESP_BOX_3 || BOARD_TYPE_ESP_BOX || BOARD_TYPE_LICHUANG_DEV)
        help
            需要 ESP32 S3 与 AEC 开启，因为性能不够，不建议和微信聊天界面风格同时开启
            
    endmenu
    